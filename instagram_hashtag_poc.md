# Instagram Hashtag Ingestion POC

이 문서는 **Phantombuster Instagram Hashtag Search Export Phantom**을 **Python Cloud Run** 컨테이너에서 호출해 결과 CSV를 **BigQuery**로 스트리밍-적재하는 전체 과정을 설명합니다. 수동 HTTP 호출만으로 동작하며, 향후 **Cloud Scheduler**를 붙이면 완전 자동화로 확장할 수 있습니다.

## ⚡ 빠른 흐름

```
사용자 ─▶ Cloud Run (Flask 앱)
         │ launch POST
         ▼
    Phantombuster API
         │ fetch-output
         ▼
    결과 CSV URL(S3 presign)
         │ download
         ▼
    BigQuery insert_rows_json()
```

## 1. 사전 준비

| 항목 | 설명 |
|------|------|
| **GCP 프로젝트** | Cloud Run, BigQuery, Secret Manager API 활성화 |
| **IAM** | Cloud Run 서비스 계정 → `roles/secretmanager.secretAccessor`, `roles/bigquery.dataEditor` 부여 |
| **Phantombuster** | Instagram Hashtag Search Export Phantom 추가 후 **Agent ID** 확인, **API Key** 발급 |
| **gcloud CLI / Docker** | 로컬 빌드·배포용 도구 설치 |

## 2. 환경 변수

| 변수 | 용도 |
|------|------|
| `PHANTOMBUSTER_KEY` | Secret Manager 비밀 |
| `PB_AGENT_ID` | Instagram Hashtag Phantom ID |
| `BQ_TABLE` | `project.dataset.table` 형식 |

Secrets는 **Secret Manager → 환경 변수 주입** 방식으로 연결합니다.

## 3. BigQuery 테이블 작성

```bash
bq mk --dataset --location=asia-northeast3 instagram_poc

bq mk --table instagram_poc.posts \
  id:STRING,shortcode:STRING,username:STRING,caption:STRING,\
  like_count:INTEGER,comment_count:INTEGER,timestamp:TIMESTAMP
```

스트리밍 삽입을 위해 **Data Editor** 권한이 필요합니다.

## 4. 코드 구조

```
.
├── Dockerfile
├── main.py              # Flask 애플리케이션
└── requirements.txt
```

### `main.py` 핵심 로직 요약:

```python
# 1) POST /run {"hashtag":"kpop"}
container_id = launch_hashtag(tag)    # /agents/launch 호출
csv_url = wait_and_fetch(container_id)  # /agents/fetch-output 폴링
rows = csv.DictReader(io.StringIO(csv.decode()))
bq.insert_rows_json(BQ_TABLE, list(rows))  # BigQuery streaming
```

### **Dockerfile**(요약):

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "--bind", "0.0.0.0:$PORT", "main:app"]
```

## 5. Cloud Run 배포

```bash
gcloud builds submit --tag gcr.io/$PROJECT_ID/instagram-poc

gcloud run deploy instagram-poc \
  --image gcr.io/$PROJECT_ID/instagram-poc \
  --region asia-northeast3 \
  --memory 512Mi \
  --set-secrets /workspace/.env=projects/$PROJECT_NUMBER/secrets/PHANTOMBUSTER_KEY:latest
```

배포 URL은 `gcloud run services describe`로 확인할 수 있습니다.

## 6. 수동 실행 예시

```bash
SERVICE_URL=$(gcloud run services describe instagram-poc \
  --region=asia-northeast3 --format='value(status.url)')

curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
     -H "Content-Type: application/json" \
     -d '{"hashtag":"kpop"}' \
     ${SERVICE_URL}/run
```

Cloud Run HTTP 보호를 위해 **ID Token**을 사용합니다.

## 7. 향후 자동화 Roadmap

| 기능 | GCP 서비스 |
|------|------------|
| **정기 실행** | Cloud Scheduler → 같은 URL 호출(HTTP 타깃) |
| **모니터링** | Cloud Logging 필터 + Error Reporting 알림 |
| **대량 처리** | BigQuery Storage Write API, 또는 Dataflow Flex Template |
| **보안 강화** | 서비스 계정 세분화, 최소 권한 원칙 유지 |

## 8. 로컬 테스트

```bash
python -m venv venv && source venv/bin/activate
pip install -r requirements.txt

export PHANTOMBUSTER_KEY=...
export PB_AGENT_ID=...
export BQ_TABLE=project.dataset.posts

python main.py  # 포트 8080
```

로컬에서 `curl localhost:8080/run -d '{"hashtag":"kpop"}'` 로 확인 후 Docker 빌드·배포하세요.

## 참고 링크

- [Phantombuster 공식 사이트](https://phantombuster.com)
- [Google Cloud Platform](https://cloud.google.com)
- [Phantombuster 지원 문서](https://support.phantombuster.com)
- [Phantombuster Hub](https://hub.phantombuster.com)